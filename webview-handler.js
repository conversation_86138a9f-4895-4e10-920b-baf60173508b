// Helper function for improved whitelist checking
function isInWhitelistImproved(domain, whitelist) {
    if (!domain || !whitelist || !whitelist.length) return false;

    const normalizedDomain = domain.toLowerCase().trim();

    return whitelist.some(whitelistDomain => {
        const normalizedWhitelistDomain = whitelistDomain.toLowerCase().trim();

        // Exact match
        if (normalizedDomain === normalizedWhitelistDomain) return true;

        // Subdomain match
        if (normalizedDomain.endsWith('.' + normalizedWhitelistDomain)) return true;

        // Related domain check (Amazon-style)
        if (isRelatedDomainSimple(normalizedDomain, normalizedWhitelistDomain)) return true;

        return false;
    });
}

// Generic algorithmic related domain check for webview handler
function isRelatedDomainSimple(testDomain, whitelistedDomain) {
    // Extract domain identifiers using simplified algorithmic approach
    const extractSimpleIdentifiers = (domain) => {
        const parts = domain.split('.');
        if (parts.length < 2) return [domain];

        const identifiers = [];
        const secondLastPart = parts[parts.length - 2];

        // Add the main part
        identifiers.push(secondLastPart);

        // Extract from hyphenated domains
        if (secondLastPart.includes('-')) {
            const hyphenParts = secondLastPart.split('-');
            hyphenParts.forEach(part => {
                if (part.length >= 3) {
                    identifiers.push(part);
                }
            });
        }

        // Extract prefixes from compound words
        if (secondLastPart.length > 6) {
            for (let len = 3; len <= Math.min(8, secondLastPart.length - 2); len++) {
                identifiers.push(secondLastPart.substring(0, len));
            }
        }

        return [...new Set(identifiers)].filter(id => id.length >= 3);
    };

    const testIdentifiers = extractSimpleIdentifiers(testDomain);
    const whitelistedIdentifiers = extractSimpleIdentifiers(whitelistedDomain);

    // Check for matching identifiers
    for (const testId of testIdentifiers) {
        for (const whitelistedId of whitelistedIdentifiers) {
            if (testId === whitelistedId && testId.length >= 3) {
                return true;
            }
        }
    }

    return false;
}

// Webview event handling
function setupWebview(webview) {
    webview.addEventListener('dom-ready', () => {
        try {
            const url = webview.getURL();
            const domain = new URL(url).hostname;

            // Get settings from store
            const settings = store.get('settings') || {};
            const blockImages = settings.blockImages === 'true';
            const imageWhitelist = settings.imageWhitelist ? JSON.parse(settings.imageWhitelist) : [];

            // Check if current domain is in whitelist using improved logic
            const isWhitelisted = isInWhitelistImproved(domain, imageWhitelist);

            if (blockImages && !isWhitelisted) {
                webview.insertCSS('img, picture, source { display: none !important; }');
            }
        } catch (err) {
            console.error('Error in dom-ready handler:', err);
        }
    });

    webview.addEventListener('did-navigate', (event) => {
        console.log('Navigation occurred:', event.url);
    });

    webview.addEventListener('page-title-updated', (event) => {
        console.log('Page title updated:', event.title);
    });

    webview.addEventListener('did-fail-load', (event) => {
        console.log('Page failed to load:', event.errorCode, event.errorDescription);
    });

    webview.addEventListener('render-process-gone', (event) => {
        console.log('Webview crashed:', event.reason);
    });

    webview.addEventListener('unresponsive', () => {
        console.log('Webview became unresponsive');
    });

    webview.addEventListener('responsive', () => {
        console.log('Webview became responsive again');
    });

    // Set up webview preferences
    webview.setAttribute('webpreferences', 'images=true, javascript=true, webSecurity=true, contextIsolation=true');
    webview.setAttribute('partition', 'persist:main');
    webview.setAttribute('allowpopups', 'true');
    webview.setAttribute('preload', './preload.js');
}

// Function to create a new webview
function createWebview(url, tabId) {
    const webview = document.createElement('webview');
    const webviewContainer = document.getElementById('webview-container');
    const tabsContainer = document.getElementById('tabs');
    const tab = document.createElement('div');
    tab.className = 'tab';
    tab.textContent = 'New Tab';
    tab.dataset.tabId = tabId;

    setupWebview(webview);

    // Set src and style
    webview.src = url;
    webview.style.display = 'none';
    webview.style.width = '100%';
    webview.style.height = '100%';
    webview.style.flex = '1';

    // Add to container
    webviewContainer.appendChild(webview);

    // Add to tabs array
    tabs.push({ id: tabId, webview: webview, element: tab });
    tabsContainer.appendChild(tab);

    // Set as active tab
    setActiveTab(tabId);

    // Show webview
    webview.style.display = 'flex';
}

// Export functions
module.exports = {
    setupWebview,
    createWebview
};